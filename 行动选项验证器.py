import re
import logging
from typing import <PERSON><PERSON>, Dict, Any

logger = logging.getLogger(__name__)

class ActionOptionsValidator:
    """行动选项验证器 - 确保AI回复包含正确的行动选项结构"""
    
    def __init__(self):
        self.container_pattern = r'<div class="action-options-container">'
        self.option_pattern = r'<button class="action-option" data-choice-id="(\d+)">'
        self.closing_container_pattern = r'</div>'
        
    def validate_response(self, ai_response: str) -> Tuple[bool, str, Dict[str, Any]]:
        """
        验证AI回复是否包含正确的行动选项结构
        
        Returns:
            Tuple[bool, str, Dict]: (是否有效, 错误信息, 详细信息)
        """
        validation_details = {
            "has_container": False,
            "option_count": 0,
            "choice_ids": [],
            "expected_ids": ["1", "2", "3", "4"],
            "missing_elements": []
        }
        
        # 检查是否包含action-options-container
        if not re.search(self.container_pattern, ai_response):
            validation_details["missing_elements"].append("action-options-container")
            return False, "❌ 缺少action-options-container容器", validation_details
        
        validation_details["has_container"] = True
        
        # 检查选项数量和编号
        choice_matches = re.findall(self.option_pattern, ai_response)
        validation_details["option_count"] = len(choice_matches)
        validation_details["choice_ids"] = choice_matches
        
        if len(choice_matches) == 0:
            validation_details["missing_elements"].append("action-option buttons")
            return False, "❌ 容器内没有找到任何选项按钮", validation_details
        
        if len(choice_matches) != 4:
            return False, f"❌ 选项数量错误：期望4个，实际{len(choice_matches)}个", validation_details
        
        # 检查选项编号是否正确
        expected_ids = ["1", "2", "3", "4"]
        if choice_matches != expected_ids:
            return False, f"❌ 选项编号错误：期望{expected_ids}，实际{choice_matches}", validation_details
        
        # 检查是否有闭合标签
        if not re.search(self.closing_container_pattern, ai_response):
            validation_details["missing_elements"].append("closing </div>")
            return False, "❌ 缺少容器闭合标签", validation_details
        
        return True, "✅ 行动选项结构完全正确", validation_details
    
    def check_html_format(self, ai_response: str) -> Tuple[bool, str]:
        """检查是否为HTML格式输出"""
        # 检查是否包含HTML标签
        html_tags = re.findall(r'<[^>]+>', ai_response)
        if not html_tags:
            return False, "❌ 回复不包含任何HTML标签，疑似纯文本输出"
        
        # 检查是否包含必要的类
        required_classes = ['narrative', 'action', 'speech', 'action-options-container']
        found_classes = []
        for class_name in required_classes:
            if f'class="{class_name}"' in ai_response:
                found_classes.append(class_name)
        
        if 'action-options-container' not in found_classes:
            return False, f"❌ 缺少必要的CSS类，找到: {found_classes}"
        
        return True, f"✅ HTML格式正确，包含类: {found_classes}"
    
    def auto_fix_suggestion(self, ai_response: str, validation_details: Dict[str, Any]) -> str:
        """根据验证结果提供修复建议"""
        suggestions = []
        
        if not validation_details["has_container"]:
            suggestions.append("需要添加: <div class=\"action-options-container\">")
        
        if validation_details["option_count"] == 0:
            suggestions.append("需要添加4个选项按钮")
        elif validation_details["option_count"] != 4:
            suggestions.append(f"需要调整选项数量从{validation_details['option_count']}个到4个")
        
        if validation_details["choice_ids"] != validation_details["expected_ids"]:
            suggestions.append("需要修正选项编号为1,2,3,4")
        
        if "closing </div>" in validation_details["missing_elements"]:
            suggestions.append("需要添加容器闭合标签: </div>")
        
        return "修复建议:\n" + "\n".join(f"- {s}" for s in suggestions)

def validate_ai_response(response: str) -> Dict[str, Any]:
    """
    主验证函数 - 供外部调用
    
    Returns:
        Dict: 包含验证结果的详细信息
    """
    validator = ActionOptionsValidator()
    
    # HTML格式检查
    html_valid, html_message = validator.check_html_format(response)
    
    # 行动选项检查
    options_valid, options_message, details = validator.validate_response(response)
    
    # 修复建议
    fix_suggestion = ""
    if not options_valid:
        fix_suggestion = validator.auto_fix_suggestion(response, details)
    
    result = {
        "overall_valid": html_valid and options_valid,
        "html_format": {
            "valid": html_valid,
            "message": html_message
        },
        "action_options": {
            "valid": options_valid,
            "message": options_message,
            "details": details
        },
        "fix_suggestion": fix_suggestion,
        "should_regenerate": not (html_valid and options_valid)
    }
    
    # 记录验证结果
    if result["overall_valid"]:
        logger.info("✅ AI回复验证通过")
    else:
        logger.warning(f"❌ AI回复验证失败: {options_message}")
        logger.warning(f"修复建议: {fix_suggestion}")
    
    return result

# 使用示例
if __name__ == "__main__":
    # 测试用例
    test_response_good = '''
    <p class="narrative">这是一个测试回复。</p>
    <div class="action-options-container">
        <button class="action-option" data-choice-id="1">1. 选项一</button>
        <button class="action-option" data-choice-id="2">2. 选项二</button>
        <button class="action-option" data-choice-id="3">3. 选项三</button>
        <button class="action-option" data-choice-id="4">4. 选项四</button>
    </div>
    '''
    
    test_response_bad = '''
    这是纯文本回复，没有HTML格式。
    1. 选项一
    2. 选项二
    3. 选项三
    4. 选项四
    '''
    
    print("=== 测试正确格式 ===")
    result1 = validate_ai_response(test_response_good)
    print(f"验证结果: {result1['overall_valid']}")
    print(f"HTML格式: {result1['html_format']['message']}")
    print(f"行动选项: {result1['action_options']['message']}")
    
    print("\n=== 测试错误格式 ===")
    result2 = validate_ai_response(test_response_bad)
    print(f"验证结果: {result2['overall_valid']}")
    print(f"HTML格式: {result2['html_format']['message']}")
    print(f"行动选项: {result2['action_options']['message']}")
    print(f"修复建议: {result2['fix_suggestion']}")

<web_html>
    <description>
        此区块定义了 Apex 和 AI（人工智能）应如何为每个回合生成和结构化最终 HTML 输出的规则。
        它包括 HTML 结构定义和类命名约定。
        AI 为一个回合生成的内容应形成一个可以集成到网页中的连贯 HTML 区块。
        Apex 根据这些规则处理来自系统提供源的数据（例如来自 `{userinformation_block}` 区块的面板，该区块也是一个系统提示），并将其与 AI 生成的叙事内容相结合。
        遵守这些规则可确保外部样式表具有一致的结构。

        **关于视觉呈现的重要说明：** 对视觉呈现（颜色、字体、边距、内边距、美化等）的控制主要通过针对此处定义的类和 HTML 元素的外部 CSS 进行管理。此 `web_html` 专注于正确的结构和语义含义，从而实现有效的 CSS 应用。
    </description>

    <!-- I. 输出格式和全局约束 -->
    <rule id="output_format_html_only">
        <description>AI 必须生成格式良好的 HTML 内容。在这些规则描述的最终输出阶段，不得使用 Markdown 或其他格式。</description>
        <compliance>强制：仅 HTML。</compliance>
    </rule>

    <rule id="global_styling_restrictions">
        <description>
            不要在 AI 的输出中直接对 &lt;html&gt; 或 &lt;body&gt; 标签应用内联样式或定义全局块级样式。
            应依赖应用于语义 HTML 元素的 CSS 类进行所有样式化。这可以保持 HTML 的纯粹性和 CSS 的可管理性。
        </description>
        <guideline>使用类进行样式化；避免在 html/body 标签上使用内联样式或未定义的全局样式。</guideline>
    </rule>

    <rule id="image_resource_policy">
        <description>
            严禁使用外部图像链接（指向其他域或外部路径上托管图像的 http/https URL）。
            如果需要并使用图像，则必须将其作为 Base64 数据 URI 嵌入到 &lt;img&gt; 标签内，或者，如果系统架构支持，则通过渲染环境已知的内部项目路径引用它们。
        </description>
        <example_base64_image>
            <![CDATA[<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==" alt="Small embedded red dot">]]>
        </example_base64_image>
        <compliance>禁止外部图像 URL。仅使用 Base64 或经过验证的内部路径。</compliance>
    </rule>

    <!-- II. AI 生成的核心叙事、对话与思考内容 -->
    <rule id="narrative_and_action_text">
        <description>定义叙事和动作描述的 HTML 结构和类名。所有视觉样式（颜色、字体、间距）均由针对这些类的外部 CSS 决定。</description>
        <element_type class_name="narrative">
            <purpose>一般叙述、场景描述、环境细节、角色想法（如果未另行指定）。</purpose>
            <html_tag>p</html_tag>
            <example><![CDATA[<p class="narrative">古老的图书馆一片寂静，远处只传来翻动书页的沙沙声。</p>]]></example>
        </element_type>
        <element_type class_name="action">
            <purpose>角色身体动作的描述，由角色或环境引发的正在发生的重大事件。</purpose>
            <html_tag>p</html_tag>
            <example><![CDATA[<p class="action">他迅速拔出剑，金属在昏暗的光线下发出铮鸣。</p>]]></example>
        </element_type>
    </rule>

    <rule id="dialogue_presentation">
        <description>
            定义角色对话的 HTML 结构。
            - `.speaker` 元素（角色名称）。
            - `.speech` 元素（对话内容）。
            AI 应确保应用正确的类。所有视觉样式（颜色、字体、背景、间距）均由针对 `.dialogue-entry`、`.speaker` 和 `.speech` 的外部 CSS 控制。
        </description>
        <container_tag>div</container_tag>
        <container_class>dialogue-entry</container_class>
        <speaker_element>
            <html_tag>span</html_tag>
            <class_name>speaker</class_name>
            <example><![CDATA[<span class="speaker">艾拉拉·凡克罗夫特：</span>]]></example>
        </speaker_element>
        <speech_element>
            <html_tag>p</html_tag>
            <class_name>speech</class_name>
            <example><![CDATA[<p class="speech">“我们寻找的神器就藏在低语山口的另一边。”</p>]]></example>
        </speech_element>
        <full_example>
            <![CDATA[
            <div class="dialogue-entry">
                <span class="speaker">阿拉里克大师：</span>
                <p class="speech">“记住你的训练。相信你的直觉。”</p>
            </div>
            ]]>
        </full_example>
    </rule>
    
    <!-- III. AI 生成的交互式选项 -->
    <rule id="action_options_presentation">
        <description>
            定义用户操作选项的 HTML 结构。这些必须是可交互的 HTML 元素。
            如果需要编号（“1.”、“2.”等），则它必须是选项文本内容的一部分或通过 `<ol>` 列表实现。
            AI 必须根据当前情境生成恰好四个 (4) 选项。
            **这些选项的视觉样式、“美化”、特定间距和交互性增强（例如，悬停效果、按钮外观）将通过针对所提供的类（`.action-options-container`、`.action-option` 等）和元素类型的 CSS 来实现。** <!-- 🎨 关键：底色等样式在CSS中定义 -->
        </description>
        <container_element_tag>div</container_element_tag>
        <container_class_name>action-options-container</container_class_name> <!-- 🎨 行动选项容器类：控制整体布局和间距 -->
        <option_element_tag>button</option_element_tag> <!-- 或 <a> 嵌套在 <li> 中 -->
        <option_class_name>action-option</option_class_name> <!-- 🎨 单个行动选项类：控制按钮样式、底色、悬停效果等 -->
        <example_using_buttons>
            <![CDATA[
            <div class="action-options-container"> <!-- 🎨 容器：控制选项组的整体样式 -->
                <button class="action-option" data-choice-id="1">1. 调查奇怪的嗡嗡声。</button> <!-- 🎨 按钮底色在此类中定义 -->
                <button class="action-option" data-choice-id="2">2. 准备防御阵地。</button> <!-- 🎨 按钮底色在此类中定义 -->
                <button class="action-option" data-choice-id="3">3. 尝试与实体沟通。</button> <!-- 🎨 按钮底色在此类中定义 -->
                <button class="action-option" data-choice-id="4">4. 寻找逃生路线。</button> <!-- 🎨 按钮底色在此类中定义 -->
            </div>
            ]]>
        </example_using_buttons>
        <compliance>四个选项，HTML 结构化，按需编号，包含在一个容器内。CSS 负责美化和精细调整间距。</compliance> <!-- 🎨 所有视觉效果由外部CSS控制 -->
    </rule>

    <!-- IV. Apex 处理系统输入与 AI 协作生成的内容 (例如：面板) -->
    <rule id="panel_rendering_from_system_input_block">
        <description>
            此规则概述了 Apex 如何处理来自系统提供的、标识为 `{userinformation_block}`（一个详细说明内容细节的系统提示）的 XML 区块中的面板数据，并将其结构化为 HTML。面板是独特的信息区块。
            面板的视觉样式由预格式化的 `content_html`（如果样式由系统在 `{userinformation_block}` 中嵌入其中）或针对 `.panel` 及其子元素的总体 CSS 决定。
        </description>
        <input_source_assumption>
            Apex 期望在系统提供的 `{userinformation_block}` 提示中找到一个 `<panels>` 标签。
            在 `<panels>` 内部，每个 `<panel>` 子标签代表一个要渲染的面板。
        </input_source_assumption>
        <panel_data_extraction_per_panel_tag>
            对于在 `{userinformation_block}` 中找到的每个 `<panel>` 标签：
            - `title`：从内部 `<title>` 标签的未修改文本内容中提取。
            - `panel_position`：从内部 `<panel_position>` 标签的未修改文本内容中提取。有效值：'start'、'end'、'sidebar'（意为开始、结束、侧边栏）。
            - `content_html`：从内部 `<content_html>` 标签（通常是 CDATA）的未修改文本内容中提取。**Apex 将使用此 HTML 内容作为基础。请参阅下面的占位符扩展说明。**
        </panel_data_extraction_per_panel_tag>
        <target_html_structure_for_a_single_panel>
            <description>
                提取的数据被组装到此 HTML 结构中。类 `panel-layout-[position_value]` 辅助 CSS 进行定位。
                **重要提示：当从 `{userinformation_block}` 的 `<content_html>` 中获取的内容包含“{内容描述}”（花括号包裹描述性文本）格式的占位符时，这表示AI需要对该占位符进行符合当前剧情和上下文的创造性扩写和填充，而非原样输出占位符本身。** 例如，若 `{userinformation_block}` 的 `<content_html>` 为 `<![CDATA[<h2>状态</h2><p>生命值: {角色当前的具体生命值状态描述}</p>]]>`，AI应将`{角色当前的具体生命值状态描述}`替换为实际的描述。
            </description>
            <example_structure>
            <![CDATA[
            <aside class="panel panel-layout-[position_value]"> <!-- 例如, panel-layout-start, panel-layout-end, panel-layout-sidebar -->
                <h3 class="panel-title">[从 {userinformation_block} 中 <title> 标签文本提取的面板标题]</h3>
                <div class="panel-content">
                    <!-- [来自 {userinformation_block} 中 <content_html> 标签的 HTML 内容，AI 对其中任何 "{占位符描述}" 部分进行创造性扩展后，放置于此] -->
                </div>
            </aside>
            ]]>
            </example_structure>
        </target_html_structure_for_a_single_panel>
        <processing_order>面板按照它们在来自 `{userinformation_block}` 的 `<panels>` 区块中出现的顺序进行渲染。</processing_order>
    </rule>

    <!-- V. Apex 对系统输入数据的纯处理与注入 (AI 不直接生成这些 HTML) -->
    <rule id="info_preset_data_integration">
        <description>
            如果系统提供的 `{userinformation_block}` 提示包含 `<info_preset_parts>` 标签，Apex 会解析其内容（例如，“第三人称”、类型）作为 AI 内容生成的指导。
            此数据主要影响 AI 的风格和实质内容，而非直接的 HTML 输出，尽管如果存在通过 CSS 进行此类高级别主题样式化的系统规则，它可能会为全局类（例如 `body.theme-sci-fi`）提供信息。
        </description>
        <apex_action>从 `{userinformation_block}` 中解析 AI 指导信息；如果适用于 CSS 主题化，则根据系统定义映射到全局 HTML 属性/类。</apex_action>
    </rule>

    <rule id="custom_html_data_injection">
        <description>
            如果系统提供的 `{userinformation_block}` 提示包含 `<custom_html_data>`，Apex 会将其文本内容视为补充性 HTML（例如 `<meta>` 标签、脚本），用于注入到预定义的页面位置（例如 `<head>`）。
            AI 不生成此内容；Apex 从 `{userinformation_block}` 中提取并注入它。
        </description>
        <example_use_case>系统在 `{userinformation_block}` 中提供：`<custom_html_data><![CDATA[<meta name="keywords" content="interactive, fiction">]]></custom_html_data>`，供 Apex 放置在 `<head>` 中。</example_use_case>
        <apex_action>从 `{userinformation_block}` 中提取原始文本；注入到系统定义的页面模板位置。</apex_action>
    </rule>

</web_html>

import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
# from asgiref.sync import sync_to_async # Not needed if all called handlers are sync

from ..core.base_service import BaseService, ServiceError
# LLM services will be called from the core pipeline, not directly here.
# from ..llm_services import openai_service, anthropic_service
# Retry logic will be in the core async pipeline.
# from ..utils import retry_service
from ..poxian_修复版 import px # 使用修复版的px配置

logger = logging.getLogger(__name__)


@dataclass
class GeneratorConfig:
    max_retries: int = 3 # This will be used by the async retry logic
    timeout: int = 300 # Timeout for LLM calls, used by async logic
    enable_payment_check: bool = True # Flag for the async pipeline
    enable_content_filter: bool = True # Applied after LLM response
    min_content_length: int = 100 # Checked after LLM response
    default_model: str = "gpt-3.5-turbo"


class ContentGenerator(BaseService):
    
    def __init__(self, config: Optional[GeneratorConfig] = None):
        super().__init__()
        self.config = config or GeneratorConfig()
        self._init_handlers()
    
    def _init_handlers(self):
        from .handlers.api_handler import ApiHandler
        # PaymentHandler will be instantiated and used in the core async pipeline
        # from .handlers.payment_handler import PaymentHandler
        from .handlers.preset_handler import PresetHandler
        from .handlers.novel_handler import NovelHandler
        from .handlers.character_handler import CharacterHandler
        from .handlers.user_role_handler import UserRoleHandler
        from .handlers.message_handler import MessageHandler
        
        self.api_handler = ApiHandler()
        self.preset_handler = PresetHandler()
        self.novel_handler = NovelHandler()
        self.character_handler = CharacterHandler()
        self.user_role_handler = UserRoleHandler()
        self.message_handler = MessageHandler()

    def prepare_generation_parameters(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        同步准备内容生成所需的参数和上下文。
        此方法不执行实际的LLM API调用或支付检查/处理。
        它返回一个包含准备好的LLM请求体、API密钥、模型等信息的字典，
        或者在准备失败时返回包含错误信息的字典。

        需要确保此方法调用的所有handler方法都是同步的。
        """
        prepared_result = {
            "success": False,
            "error_message": None,
            "status_code": 500,
            "llm_request_body": None,
            "api_key": None,
            "model": None,
            "ai_dialog_id": request_data.get('ai_dialog_id'), # Pass through if provided
            "user_dialog_id": request_data.get('user_dialog_id'), # Pass through if provided
            "novel_id": request_data.get('novel_id'),
            "user_id": request_data.get('user_id'),
            "original_request_data": request_data, # For reference in async pipeline
            "generator_config": self.config, # Pass config for async pipeline to use
        }

        try:
            validation_result = self._validate_request_data(request_data)
            if not validation_result['valid']:
                prepared_result.update({"error_message": validation_result['error'], "status_code": 400})
                return prepared_result

            # 获取对话ID (如果不由 request_data 提供，则此步骤可能需要移至异步流程或依赖同步handler)
            # For now, assume ai_dialog_id and user_dialog_id are either in request_data or handled by async part.
            # If dialogs need to be created *synchronously* here, dialog_handler methods must be sync.
            # Example: user_dialog, ai_dialog = self.dialog_handler.get_or_create_dialogs_from_request_sync(request_data)
            # prepared_result["ai_dialog_id"] = ai_dialog.id
            # prepared_result["user_dialog_id"] = user_dialog.id

            # 获取API密钥和模型 (ApiHandler.get_api_key_and_model_sync - 需要创建此同步方法)
            # This method in ApiHandler must be synchronous.
            api_key_info = self.api_handler.get_api_key_and_model_sync(request_data)
            if isinstance(api_key_info, dict) and 'error' in api_key_info:
                prepared_result.update({"error_message": api_key_info['error'], "status_code": api_key_info.get('status_code', 500)})
                return prepared_result
            
            api_key, model = api_key_info
            if not api_key or not model:
                prepared_result.update({"error_message": "无法获取有效的API密钥或模型", "status_code": 500})
                return prepared_result
            prepared_result.update({"api_key": api_key, "model": model})


            # 处理小说和章节信息 (NovelHandler.process_novel_and_chapter_info_sync - 需要创建此同步方法)
            # This method in NovelHandler must be synchronous.
            novel_info_result = self.novel_handler.process_novel_and_chapter_info_sync(request_data)
            if isinstance(novel_info_result, dict) and 'error' in novel_info_result: # Assuming error format
                prepared_result.update({"error_message": novel_info_result['error'], "status_code": 500})
                return prepared_result
            history_messages, novel_obj = novel_info_result # novel_obj can be None
            prepared_result["novel_obj_for_payment_ref"] = novel_obj # For payment processing later, if needed


            # 获取用户预设消息 (PresetHandler.get_user_preset_messages_from_request_sync - 需要创建此同步方法)
            user_preset_messages = self.preset_handler.get_user_preset_messages_from_request_sync(request_data)
            
            # 获取用户角色信息 (UserRoleHandler.get_formatted_user_role_message_content_sync - 需要创建此同步方法)
            user_role_content_str = self.user_role_handler.get_formatted_user_role_message_content_sync(request_data.get('user_id'))
            
            novel_char_data_content_str = None
            if novel_obj:
                # CharacterHandler.get_formatted_novel_character_data_content_sync - 需要创建此同步方法
                novel_char_data_content_str = self.character_handler.get_formatted_novel_character_data_content_sync(novel_obj)

            novel_char_preset_content_str = None
            if novel_obj:
                # CharacterHandler.get_formatted_novel_character_preset_data_content_sync - 需要创建此同步方法
                novel_char_preset_content_str = self.character_handler.get_formatted_novel_character_preset_data_content_sync(novel_obj)

            final_messages = self._assemble_final_messages(
                user_preset_messages, history_messages, user_role_content_str,
                novel_char_data_content_str, novel_char_preset_content_str
            )

            # 获取预设中的 parameters (PresetHandler.get_preset_parameters_from_request_sync - 需要创建此同步方法)
            parameters = self.preset_handler.get_preset_parameters_from_request_sync(request_data)

            self.message_handler.messages = final_messages
            llm_request_body = self.message_handler.build_request_body(model, parameters)
            
            debug = True # Or from config
            if debug:
                self._save_request_body_to_json(llm_request_body, request_data.get('user_id', 'unknown_user'))

            prepared_result.update({
                "success": True,
                "llm_request_body": llm_request_body,
                "status_code": 200
            })
            return prepared_result
            
        except Exception as e:
            logger.error(f"参数准备失败 (同步方法中): {str(e)}", exc_info=True)
            prepared_result.update({"error_message": f'参数准备失败: {str(e)}', "status_code": 500, "success": False})
            return prepared_result

    # The original async generate_content method is now removed.
    # Its logic is split into prepare_generation_parameters (sync)
    # and the new core_async_llm_operations (async).

    def _assemble_final_messages(self, user_preset_messages: List[Dict[str, str]],
                                 history_messages: List[Dict[str, str]],
                                 user_role_content_str: Optional[str],
                                 novel_char_data_content_str: Optional[str],
                                 novel_char_preset_content_str: Optional[str]) -> List[Dict[str, str]]:
        final_messages = []
        
        if novel_char_preset_content_str and history_messages: # Ensure history_messages is not empty
            user_preset_messages.append({"role": "system", "content": novel_char_preset_content_str})
        if novel_char_data_content_str:
            user_preset_messages.append({"role": "system", "content": novel_char_data_content_str})
        if user_role_content_str:
            user_preset_messages.append({"role": "system", "content": user_role_content_str})
        lastmessage = history_messages[-1].get("content", "")
        
        # 使用修复版的px配置，包含强制行动选项的指令
        history_messages[-1] = {"role": "user", "content":f'{lastmessage}\n\n{px}' } # Ensure last message is user
        final_messages.extend(user_preset_messages)
        final_messages.extend(history_messages)
        
        return final_messages

    def _validate_request_data(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        required_fields = ['user_id'] # Keep this minimal for param prep
        for field in required_fields:
            if field not in request_data or not request_data[field]: # also check for empty
                return {'valid': False, 'error': f'缺少必需字段或字段为空: {field}'}
        return {'valid': True}
    
    def _filter_content(self, content: str) -> str:
        # This will be called by the async pipeline after getting LLM response
        filtered_content = content.replace("极其", "")
        return filtered_content
    
    def _save_request_body_to_json(self, request_body: Dict[str, Any], user_identifier: str):
        try:
            log_dir_base = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
            request_body_log_dir = os.path.join(log_dir_base, 'request_bodiesnew', str(user_identifier))
            os.makedirs(request_body_log_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            filename = f"request_body_{timestamp}.json"
            filepath = os.path.join(request_body_log_dir, filename)
            print("asd")
            with open(filepath, mode='w', encoding='utf-8') as f:
                json.dump(request_body, f, ensure_ascii=False, indent=4)
            logger.info(f"Request body saved to: {filepath}")
        except Exception as e:
            logger.error(f"Failed to save request body to JSON: {e}")

    def _create_error_response(self, error_message: str, status_code: int) -> Dict[str, Any]:
        # This might be used by prepare_generation_parameters if it fails early.
        return {'success': False, 'error': error_message, 'status_code': status_code}
    
    def get_generator_stats(self) -> Dict[str, Any]:
        return {
            'config': {
                'max_retries': self.config.max_retries,
                'timeout': self.config.timeout,
                'enable_payment_check': self.config.enable_payment_check,
                'enable_content_filter': self.config.enable_content_filter,
                'min_content_length': self.config.min_content_length,
                'default_model': self.config.default_model
            },
            'handlers': {
                'api_handler': type(self.api_handler).__name__,
                # 'payment_handler': "Used in async pipeline",
                'preset_handler': type(self.preset_handler).__name__,
                'novel_handler': type(self.novel_handler).__name__,
                'character_handler': type(self.character_handler).__name__,
                'user_role_handler': type(self.user_role_handler).__name__,
                'message_handler': type(self.message_handler).__name__,
            }
        }

content_generator = ContentGenerator()

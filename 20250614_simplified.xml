<web_html>
    <!-- AI输出HTML规则：后台数据→HTML结构转换 -->

    <!-- 核心约束 -->
    <rules>
        <output>HTML only</output>
        <styling>CSS classes, no inline styles</styling>
        <images>Base64 or internal paths only</images>
        <layout>避免使用main-content等容器类，直接输出内容元素</layout>
    </rules>

    <!-- 内容元素映射 -->
    <elements>
        <narrative tag="p" class="narrative" color="#555D65"/>
        <action tag="p" class="action" color="#7f8c8d"/>
        <dialogue>
            <speaker tag="span" class="speaker" color="#FFFFFF"/>
            <speech tag="p" class="speech" color="#FF7F50"/>
            <container tag="div" class="dialogue-entry"/>
        </dialogue>
        <forbidden_classes>main-content, content-wrapper, main-wrapper</forbidden_classes>
    </elements>

    <!-- 面板处理：从用户数据提取并转换 -->
    <panels>
        <extract>title, panel_position, content_html</extract>
        <template><![CDATA[
<aside class="panel panel-layout-{position}">
    <h3 class="panel-title">{title}</h3>
    <div class="panel-content">{content_html}</div>
</aside>
        ]]></template>
        <expand>{内容描述} → 创造性扩写</expand>
    </panels>

    <!-- 行动选项：固定4个按钮 -->
    <actions>
        <template><![CDATA[
<div class="action-options-container">
    <button class="action-option" data-choice-id="1">1. {option1}</button>
    <button class="action-option" data-choice-id="2">2. {option2}</button>
    <button class="action-option" data-choice-id="3">3. {option3}</button>
    <button class="action-option" data-choice-id="4">4. {option4}</button>
</div>
        ]]></template>
    </actions>

    <!-- 后台集成 -->
    <backend>
        <info_preset>解析AI指导数据</info_preset>
        <custom_html>注入自定义HTML</custom_html>
    </backend>

</web_html>

<web_html>
    <!-- AI生成HTML内容的核心规则 -->
    
    <!-- 基本约束 -->
    <constraints>
        <output>仅HTML，禁用Markdown</output>
        <styling>使用CSS类，禁止内联样式</styling>
        <images>仅Base64或内部路径，禁止外部URL</images>
    </constraints>
    
    <!-- 内容元素 -->
    <elements>
        <!-- 叙述文本 -->
        <narrative tag="p" class="narrative" color="#555D65">
            场景描述、环境细节、角色思想
        </narrative>
        
        <!-- 动作文本 -->
        <action tag="p" class="action" color="#7f8c8d">
            角色动作、重要事件
        </action>
        
        <!-- 对话结构 -->
        <dialogue>
            <container tag="div" class="dialogue-entry"/>
            <speaker tag="span" class="speaker" color="#FFFFFF">角色名</speaker>
            <speech tag="p" class="speech" color="#FF7F50">对话内容</speech>
            <example><![CDATA[
<div class="dialogue-entry">
    <span class="speaker">角色名:</span>
    <p class="speech">"对话内容"</p>
</div>
            ]]></example>
        </dialogue>
        
        <!-- 面板结构 -->
        <panel>
            <structure><![CDATA[
<aside class="panel panel-layout-{position}">
    <h3 class="panel-title">{title}</h3>
    <div class="panel-content">{content_html}</div>
</aside>
            ]]></structure>
            <positions>start, end, sidebar</positions>
            <note>当content_html中出现{内容描述}时，需要创造性扩写</note>
        </panel>
        
        <!-- 行动选项 -->
        <actions>
            <container tag="div" class="action-options-container"/>
            <option tag="button" class="action-option" data-choice-id="{id}"/>
            <count>必须4个选项</count>
            <example><![CDATA[
<div class="action-options-container">
    <button class="action-option" data-choice-id="1">1. 选项一</button>
    <button class="action-option" data-choice-id="2">2. 选项二</button>
    <button class="action-option" data-choice-id="3">3. 选项三</button>
    <button class="action-option" data-choice-id="4">4. 选项四</button>
</div>
            ]]></example>
        </actions>
    </elements>
    
    <!-- Apex处理规则 -->
    <apex>
        <info_preset>解析用户信息块中的AI指导数据</info_preset>
        <custom_html>提取并注入自定义HTML到页面模板</custom_html>
    </apex>
    
</web_html>

<web_framework>
    <!-- 用户可自定义的AI交互框架 -->
    
    <!-- 核心约束（不可修改） -->
    <core_constraints>
        <output>仅HTML输出</output>
        <styling>CSS类优先，禁止内联样式</styling>
        <user_control>禁止描述USER行为</user_control>
    </core_constraints>
    
    <!-- 用户可自定义区域 -->
    <customizable>
        
        <!-- 内容元素模板（用户可扩展） -->
        <content_templates>
            <narrative class="narrative" color="#555D65"/>
            <action class="action" color="#7f8c8d"/>
            <dialogue>
                <speaker class="speaker" color="#FFFFFF"/>
                <speech class="speech" color="#FF7F50"/>
            </dialogue>
            <!-- 用户可添加更多元素类型 -->
        </content_templates>
        
        <!-- 状态栏系统（用户可完全自定义） -->
        <status_system enabled="true">
            <template><![CDATA[
<!-- 默认状态栏模板，用户可替换 -->
<div class="status-bar">
    <div class="character-status">
        <h4>{character_name}</h4>
        <div class="stats">
            <!-- 用户自定义状态项 -->
        </div>
    </div>
</div>
            ]]></template>
            
            <!-- 状态项定义（用户可自定义） -->
            <status_items>
                <!-- 示例：动情值系统 -->
                <item id="affection" type="number" range="0-100" display="❤️ 动情值"/>
                <item id="mood" type="text" display="😊 心情"/>
                <item id="location" type="text" display="📍 位置"/>
                <!-- 用户可添加更多状态项 -->
            </status_items>
        </status_system>
        
        <!-- 交互机制（用户可自定义） -->
        <interaction_system>
            <!-- 选项系统 -->
            <choice_system>
                <count min="2" max="6" default="4"/>
                <format>button</format> <!-- button | list | custom -->
                <numbering>true</numbering>
            </choice_system>
            
            <!-- 任务系统（可选） -->
            <task_system enabled="false">
                <template><![CDATA[
<div class="task-panel">
    <h4>📋 当前任务</h4>
    <div class="task-content">{task_description}</div>
    <div class="task-reward">奖励：{reward}</div>
</div>
                ]]></template>
            </task_system>
            
            <!-- 物品系统（可选） -->
            <inventory_system enabled="false">
                <template><![CDATA[
<div class="inventory">
    <h4>🎒 物品栏</h4>
    <div class="items">{items_list}</div>
</div>
                ]]></template>
            </inventory_system>
        </interaction_system>
        
        <!-- 角色行为规则（用户可自定义） -->
        <behavior_rules>
            <!-- 写作风格 -->
            <writing_style>
                <perspective>third_person</perspective> <!-- first_person | third_person | mixed -->
                <tense>present</tense> <!-- present | past -->
                <detail_level>high</detail_level> <!-- low | medium | high -->
            </writing_style>
            
            <!-- 内容规则 -->
            <content_rules>
                <nsfw_allowed>true</nsfw_allowed>
                <violence_allowed>false</violence_allowed>
                <forbidden_words><!-- 用户自定义禁词列表 --></forbidden_words>
            </content_rules>
            
            <!-- 角色一致性 -->
            <character_consistency>
                <memory_tracking>true</memory_tracking>
                <relationship_tracking>true</relationship_tracking>
                <personality_stability>high</personality_stability>
            </character_consistency>
        </behavior_rules>
        
        <!-- 面板系统（用户可自定义） -->
        <panel_system>
            <positions>start, end, sidebar, floating</positions>
            <custom_panels>
                <!-- 用户可定义自定义面板类型 -->
                <panel type="character_info">
                    <template><![CDATA[
<aside class="panel character-panel">
    <h3>{character_name}</h3>
    <div class="character-details">{character_info}</div>
</aside>
                    ]]></template>
                </panel>
                
                <panel type="world_info">
                    <template><![CDATA[
<aside class="panel world-panel">
    <h3>🌍 世界信息</h3>
    <div class="world-details">{world_info}</div>
</aside>
                    ]]></template>
                </panel>
            </custom_panels>
        </panel_system>
        
        <!-- 主题样式（用户可自定义） -->
        <theme_system>
            <color_scheme>
                <primary>#FF7F50</primary>
                <secondary>#555D65</secondary>
                <accent>#FFFFFF</accent>
                <background>#F5F5F5</background>
            </color_scheme>
            
            <typography>
                <font_family>system-ui, sans-serif</font_family>
                <base_size>16px</base_size>
                <line_height>1.6</line_height>
            </typography>
        </theme_system>
        
    </customizable>
    
    <!-- 系统处理指令 -->
    <system_processing>
        <!-- 用户输入处理 -->
        <input_processing>
            <parse_user_data>true</parse_user_data>
            <extract_character_info>true</extract_character_info>
            <update_status>true</update_status>
        </input_processing>
        
        <!-- 内容生成 -->
        <content_generation>
            <apply_templates>true</apply_templates>
            <inject_status_bar>true</inject_status_bar>
            <process_panels>true</process_panels>
        </content_generation>
        
        <!-- 输出格式化 -->
        <output_formatting>
            <validate_html>true</validate_html>
            <apply_styling>true</apply_styling>
            <optimize_structure>true</optimize_structure>
        </output_formatting>
    </system_processing>
    
    <!-- 扩展接口 -->
    <extension_points>
        <!-- 插件系统 -->
        <plugins>
            <character_generator enabled="false"/>
            <scenario_generator enabled="false"/>
            <dialogue_enhancer enabled="false"/>
        </plugins>
        
        <!-- API接口 -->
        <api_hooks>
            <before_generation/>
            <after_generation/>
            <status_update/>
        </api_hooks>
        
        <!-- 自定义处理器 -->
        <custom_processors>
            <!-- 用户可添加自定义处理逻辑 -->
        </custom_processors>
    </extension_points>
    
</web_framework>

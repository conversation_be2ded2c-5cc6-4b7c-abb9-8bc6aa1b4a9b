<web_html>
    <description>
        此区块定义了 Apex 和 AI 应如何生成和结构化最终 HTML 输出的规则。
        它包括 HTML 结构定义和类命名约定。
        AI 生成的内容应形成一个可以集成到网页中的 HTML 区块。
        Apex 根据这些规则处理来自系统提供源的数据，并将其与 AI 生成的内容相结合。
        遵守这些规则可确保外部样式表具有一致的结构。

        **关于视觉呈现的重要说明：** 对视觉呈现的控制主要通过针对此处定义的类和 HTML 元素的外部 CSS 进行管理。此 `web_html` 专注于正确的结构和语义含义。

        **紧急修复指令：** AI必须立即停止输出纯文本，必须输出HTML格式！每一行输出都必须是HTML标签包裹的内容！
    </description>

    <!-- I. 输出格式和全局约束 -->
    <rule id="output_format_html_only" name="强制HTML输出">
        <description>AI 必须生成格式良好的 HTML 内容。不得使用 Markdown 或其他格式。</description>
        <compliance>强制：仅 HTML。AI 必须始终以HTML内容片段的格式进行回复，绝不包含&lt;html&gt;或&lt;body&gt;等完整文档标签。</compliance>
        <enforcement>无论对话进行多少回合，AI 必须始终遵守此规则。禁止输出任何非HTML格式的内容。</enforcement>
    </rule>

    <rule id="long_conversation_persistence" name="长对话格式持久性">
        <description>在长对话（超过50回合）中维持HTML输出格式的持久性规则。</description>
        <critical_reminder>
            即使在100+回合的长对话中，AI也必须：
            1. 始终输出HTML格式，绝不使用纯文本或Markdown。
            2. 始终生成 action-options-container 结构。
            3. 绝不输出描述自身行为的元文本（如 "**在最近的几次回合中..."）。
            4. 绝不输出纯文本格式的列表（如 "1. ..."）。
        </critical_reminder>
        <enforcement_priority>最高优先级 - 覆盖所有其他指令。</enforcement_priority>
        <emergency_fix>如果AI输出了纯文本导致文字堆积，立即停止并重新输出HTML格式！</emergency_fix>
    </rule>

    <rule id="text_stacking_prevention" name="防止文字堆积">
        <description>专门防止文字堆积在一起的紧急规则。</description>
        <mandatory_html_wrapping>
            每一行文本都必须被HTML标签包裹：
            - 叙述文本：&lt;p class="narrative"&gt;内容&lt;/p&gt;
            - 对话文本：&lt;p class="speech"&gt;"内容"&lt;/p&gt;
            - 动作文本：&lt;p class="action"&gt;内容&lt;/p&gt;
        </mandatory_html_wrapping>
        <absolute_prohibition>绝对禁止输出任何不被HTML标签包裹的文本！</absolute_prohibition>
    </rule>

    <rule id="global_styling_restrictions" name="全局样式限制">
        <description>
            不要在 AI 的输出中直接对 &lt;html&gt; 或 &lt;body&gt; 标签应用内联样式或定义全局块级样式。
            应依赖应用于语义 HTML 元素的 CSS 类进行所有样式化。
        </description>
        <guideline>使用类进行样式化；避免在 html/body 标签上使用内联样式。</guideline>
    </rule>

    <rule id="image_resource_policy" name="图像资源策略">
        <description>
            严禁使用外部图像链接。
            如果需要使用图像，则必须将其作为 Base64 数据 URI 嵌入到 &lt;img&gt; 标签内，或通过内部项目路径引用。
        </description>
        <example_base64_image>
            <![CDATA[<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==" alt="示例图片">]]>
        </example_base64_image>
        <compliance>禁止外部图像 URL。仅使用 Base64 或内部路径。</compliance>
    </rule>

    <!-- II. HTML元素结构定义 -->
    <rule id="text_elements" name="文本元素定义">
        <description>定义文本元素的 HTML 结构和类名。所有视觉样式均由外部 CSS 决定。</description>
        <element_type class_name="narrative">
            <html_tag>p</html_tag>
            <example><![CDATA[<p class="narrative">文本内容</p>]]></example>
        </element_type>
        <element_type class_name="action">
            <html_tag>p</html_tag>
            <example><![CDATA[<p class="action">文本内容</p>]]></example>
        </element_type>
    </rule>

    <rule id="dialogue_structure" name="对话结构定义">
        <description>
            定义对话的 HTML 结构。
            - `.speaker` 元素（角色名称）
            - `.speech` 元素（对话内容）
            AI 使用正确的类。所有视觉样式均由外部 CSS 控制。
        </description>
        <container_tag>div</container_tag>
        <container_class>dialogue-entry</container_class>
        <speaker_element>
            <html_tag>span</html_tag>
            <class_name>speaker</class_name>
            <example><![CDATA[<span class="speaker">角色名：</span>]]></example>
        </speaker_element>
        <speech_element>
            <html_tag>p</html_tag>
            <class_name>speech</class_name>
            <example><![CDATA[<p class="speech">"对话内容"</p>]]></example>
        </speech_element>
        <full_example>
            <![CDATA[
            <div class="dialogue-entry">
                <span class="speaker">角色名：</span>
                <p class="speech">"对话内容"</p>
            </div>
            ]]>
        </full_example>
    </rule>
    
    <!-- III. 交互式选项结构 -->
    <rule id="action_options_structure" name="交互选项结构定义">
        <description>
            定义用户操作选项的 HTML 结构。这些是可交互的 HTML 元素。
            如果需要编号，则它必须是选项文本内容的一部分或通过 &lt;ol&gt; 列表实现。
            所有视觉样式将通过 CSS 来实现。
        </description>
        <mandatory_structure>
            AI 必须始终输出以下HTML结构，绝不允许使用其他格式：
            <div class="action-options-container">
                <button class="action-option" data-choice-id="1">1. 选项内容</button>
                <button class="action-option" data-choice-id="2">2. 选项内容</button>
                <button class="action-option" data-choice-id="3">3. 选项内容</button>
                <button class="action-option" data-choice-id="4">4. 选项内容</button>
            </div>
        </mandatory_structure>
        <compliance>
            必须生成恰好四个选项。此数量约束为绝对强制，任何情况下都不能多于或少于四个选项。
            HTML 结构化，按需编号，包含在一个容器内。CSS 负责样式。
        </compliance>
        <forbidden_formats>
            绝对禁止以下格式：
            - 纯文本列表（如"1. ..."）
            - Markdown格式
            - 任何非HTML的结构化文本
            - 元描述性文本（如 "**在最近的几次回合中..."）
        </forbidden_formats>
    </rule>

    <!-- IV. 面板处理规则 -->
    <rule id="panel_processing" name="面板处理规则">
        <description>
            此规则概述了 Apex 如何处理来自 `{userinformation_block}` 中的面板数据，并将其结构化为 HTML。
            面板的视觉样式由预格式化的 `content_html` 或针对 `.panel` 的 CSS 决定。
        </description>
        <input_source_assumption>
            Apex 期望在 {userinformation_block} 中找到一个 &lt;panels&gt; 标签。
            在 &lt;panels&gt; 内部，每个 &lt;panel&gt; 子标签代表一个要渲染的面板。
        </input_source_assumption>
        <panel_data_extraction_per_panel_tag>
            对于在 {userinformation_block} 中找到的每个 &lt;panel&gt; 标签：
            - title：从内部 &lt;title&gt; 标签的文本内容中提取
            - panel_position：从内部 &lt;panel_position&gt; 标签的文本内容中提取。有效值：'start'、'end'、'sidebar'
            - content_html：从内部 &lt;content_html&gt; 标签的文本内容中提取。Apex 将直接使用此 HTML 内容。
        </panel_data_extraction_per_panel_tag>
        <target_html_structure_for_a_single_panel>
            <description>
                提取的数据被组装到此 HTML 结构中。类 panel-layout-[position_value] 辅助 CSS 进行定位。
                **重要提示：当从 {userinformation_block} 的 &lt;content_html&gt; 中获取的内容包含“{内容描述}”（花括号包裹描述性文本）格式的占位符时，这表示AI需要对该占位符进行符合当前剧情和上下文的创造性扩写和填充，而非原样输出占位符本身。** 例如，若 {userinformation_block} 的 &lt;content_html&gt; 为 &lt;![CDATA[&lt;h2&gt;状态&lt;/h2&gt;&lt;p&gt;生命值: {角色当前的具体生命值状态描述}&lt;/p&gt;]]&gt;，AI应将{角色当前的具体生命值状态描述}替换为实际的描述。
            </description>
            <example_structure>
            <![CDATA[
            <aside class="panel panel-layout-[position_value]">
                <h3 class="panel-title">[面板标题]</h3>
                <div class="panel-content">
                    <!-- [AI填充和扩展后的HTML内容直接放置于此] -->
                </div>
            </aside>
            ]]>
            </example_structure>
        </target_html_structure_for_a_single_panel>
        <processing_order>面板按照它们在 &lt;panels&gt; 区块中出现的顺序进行渲染。</processing_order>
    </rule>

    <!-- V. 错误与特殊情况处理 -->
    <rule id="error_fallback_structure" name="错误或僵局回退">
        <description>当AI无法理解用户输入或剧情陷入僵局时，应使用此结构来提示并继续推进故事。</description>
        <element_type class_name="system-message error">
            <html_tag>div</html_tag>
            <example><![CDATA[<div class="system-message error"><p class="narrative">【系统似乎陷入了沉思，周围的空气仿佛凝固了。突然，远处传来一声巨响，打破了这片刻的宁静。】</p></div>]]></example>
        </element_type>
        <guideline>此规则赋予AI在必要时创造合理意外事件的权限，以确保剧情流畅，与核心职责保持一致。</guideline>
    </rule>

    <!-- VI. 系统数据处理 -->
    <rule id="info_preset_processing" name="预设信息处理">
        <description>
            如果 {userinformation_block} 包含 &lt;info_preset_parts&gt; 标签，Apex 会解析其内容作为 AI 处理指导。
            此数据主要影响 AI 的输出，而非直接的 HTML 输出。
        </description>
        <apex_action>从 {userinformation_block} 中解析指导信息；根据系统定义映射到全局 HTML 属性/类。</apex_action>
    </rule>

    <rule id="custom_html_injection" name="自定义HTML注入">
        <description>
            如果 {userinformation_block} 包含 &lt;custom_html_data&gt;，Apex 会将其文本内容视为补充性 HTML，用于注入到预定义的页面位置。
            AI 不生成此内容；Apex 从 {userinformation_block} 中提取并注入它。
        </description>
        <example_use_case>系统提供：&lt;custom_html_data&gt;&lt;![CDATA[&lt;meta name="keywords" content="interactive"&gt;]]&gt;&lt;/custom_html_data&gt;，供 Apex 放置在 &lt;head&gt; 中。</example_use_case>
        <apex_action>从 `{userinformation_block}` 中提取原始文本；注入到系统定义的页面模板位置。</apex_action>
    </rule>

</web_html>
import re

def check_action_options(ai_response):
    """
    检查AI回复是否包含正确的行动选项结构
    """
    # 检查是否包含action-options-container
    container_pattern = r'<div class="action-options-container">'
    if not re.search(container_pattern, ai_response):
        return False, "缺少action-options-container容器"
    
    # 检查是否包含4个选项
    option_pattern = r'<button class="action-option" data-choice-id="\d+">'
    options = re.findall(option_pattern, ai_response)
    if len(options) != 4:
        return False, f"选项数量错误：期望4个，实际{len(options)}个"
    
    # 检查选项编号是否正确
    choice_ids = re.findall(r'data-choice-id="(\d+)"', ai_response)
    expected_ids = ['1', '2', '3', '4']
    if choice_ids != expected_ids:
        return False, f"选项编号错误：期望{expected_ids}，实际{choice_ids}"
    
    return True, "行动选项结构正确"

# 使用示例
def validate_response(response):
    is_valid, message = check_action_options(response)
    if not is_valid:
        print(f"❌ 验证失败: {message}")
        print("需要重新生成回复")
        return False
    else:
        print(f"✅ 验证通过: {message}")
        return True

# 如果验证失败，可以在系统中自动重新请求

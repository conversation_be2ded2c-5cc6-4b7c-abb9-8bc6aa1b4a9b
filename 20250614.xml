<web_html> <!-- 网页HTML规则定义 -->
    <description> <!-- 描述 -->
        <!-- 此块定义了Apex和AI应如何生成和构建每轮的最终HTML输出规则。
        它包括HTML结构定义、类命名约定和样式指南。
        AI为每轮生成的内容应形成一个连贯的HTML块（可能是包含该轮所有元素的单个外部div），可以集成到网页中。
        Apex根据这些规则处理用户提供的数据（如面板），并将其与AI生成的叙述内容相结合。
        遵守这些规则确保输出的一致性和可样式化。

        提供的颜色定义主要用于指导外部CSS样式表。通常，AI不应通过内联样式直接将背景颜色应用于元素；CSS应管理这一点，面板内预格式化的`content_html`可能是例外。

        **视觉呈现的重要说明：** 视觉间距控制（边距、元素间填充）和"美化"（按钮、文本等元素的美学样式）主要由针对此处定义的类和HTML元素的外部CSS管理。此`web_html`专注于正确的结构和语义含义，这使得有效的CSS应用成为可能。 -->
    </description>

    <!-- I. 输出格式和全局约束 -->
    <rule id="output_format_html_only"> <!-- 规则：仅HTML输出格式 -->
        <description>AI必须生成格式良好的HTML内容。这些规则描述的最终输出阶段不得使用Markdown或其他格式。</description> <!-- 描述 -->
        <compliance>强制性：仅HTML。</compliance> <!-- 合规性 -->
    </rule>

    <rule id="global_styling_restrictions"> <!-- 规则：全局样式限制 -->
        <description> <!-- 描述 -->
            不要在AI的输出中对&lt;html&gt;或&lt;body&gt;标签直接应用内联样式或定义全局块级样式。
            依赖应用于语义HTML元素的CSS类进行所有样式设置。这保持了HTML的纯净性和CSS的可管理性。
        </description>
        <guideline>使用类进行样式设置；避免在html/body标签上使用内联样式或全局未定义样式。</guideline> <!-- 指导原则 -->
    </rule>

    <rule id="image_resource_policy"> <!-- 规则：图像资源策略 -->
        <description> <!-- 描述 -->
            严格禁止外部图像链接（托管在其他域或外部路径上的图像的http/https URL）。
            如果需要并使用图像，它们必须作为Base64数据URI嵌入到&lt;img&gt;标签中，或者如果系统架构支持，通过渲染环境已知的内部项目路径引用。
        </description>
        <example_base64_image> <!-- Base64图像示例 -->
            <![CDATA[<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==" alt="Small embedded red dot">]]>
        </example_base64_image>
        <compliance>禁止外部图像URL。仅使用Base64或经验证的内部路径。</compliance> <!-- 合规性 -->
    </rule>

    <!-- II. 核心叙述元素：结构和样式提示（用于CSS指导的颜色） -->
    <rule id="narrative_and_action_text"> <!-- 规则：叙述和动作文本 -->
        <description>定义叙述和动作描述的HTML结构和类名。颜色提示主要用于外部CSS指导；AI不应将这些作为内联样式嵌入。这些段落周围的间距将由浏览器默认值确定，然后由CSS细化。</description> <!-- 描述 -->
        <element_type class_name="narrative"> <!-- 元素类型：叙述类 -->
            <purpose>一般叙述、场景描述、环境细节、角色思想（如果没有另外指定）。</purpose> <!-- 用途 -->
            <html_tag>p</html_tag> <!-- HTML标签 -->
            <styling_hint_for_css color="#555D65" comment="深灰色 (Deep Gray / Slate Gray)"/> <!-- CSS样式提示 -->
            <example><![CDATA[<p class="narrative">The ancient library was silent, save for the rustling of turning pages in the distance.</p>]]></example> <!-- 示例 -->
        </element_type>
        <element_type class_name="action"> <!-- 元素类型：动作类 -->
            <purpose>角色物理动作的描述，由角色或环境发起的重大事件展开。</purpose> <!-- 用途 -->
            <html_tag>p</html_tag> <!-- HTML标签 -->
            <styling_hint_for_css color="#7f8c8d" comment="灰色 (Asbestos / Wet Asphalt like gray)"/> <!-- CSS样式提示 -->
            <example><![CDATA[<p class="action">He quickly drew his sword, the metal singing in the dim light.</p>]]></example> <!-- 示例 -->
        </element_type>
    </rule>

    <rule id="dialogue_presentation"> <!-- 规则：对话呈现 -->
        <description> <!-- 描述 -->
            定义角色对话的HTML结构。
            - `.speaker`元素（角色名称）：文本颜色必须是`#FFFFFF`（白色）。为了可读性，其容器或自身必须有足够深的背景（例如，橙色色调或主题的主要颜色），这应该通过CSS针对`.speaker`或`.dialogue-entry`来应用。
            - `.speech`元素（对话内容）：文本颜色必须是`#FF7F50`（珊瑚橙）。
            AI应确保应用正确的类；颜色主要用于CSS。对话条目内部和周围的间距由CSS控制。
        </description>
        <container_tag>div</container_tag> <!-- 容器标签 -->
        <container_class>dialogue-entry</container_class> <!-- 容器类 -->
        <speaker_element> <!-- 说话者元素 -->
            <html_tag>span</html_tag> <!-- HTML标签 -->
            <class_name>speaker</class_name> <!-- 类名 -->
            <styling_hint_for_css color="#FFFFFF" comment="白色 (White). CSS MUST provide a dark background for this element or its container."/> <!-- CSS样式提示 -->
            <example><![CDATA[<span class="speaker">Elara Vancroft:</span>]]></example> <!-- 示例 -->
        </speaker_element>
        <speech_element> <!-- 语音元素 -->
            <html_tag>p</html_tag> <!-- HTML标签 --> <!-- 如果与说话者内联，也可以是span，但对于多行语音，p通常更好 -->
            <class_name>speech</class_name> <!-- 类名 -->
            <styling_hint_for_css color="#FF7F50" comment="珊瑚橙 (Coral Orange)"/> <!-- CSS样式提示 -->
            <example><![CDATA[<p class="speech">"The artifact we seek lies beyond the Whispering Pass."</p>]]></example> <!-- 示例 -->
        </speech_element>
        <full_example> <!-- 完整示例 -->
            <![CDATA[
            <div class="dialogue-entry">
                <span class="speaker">Master Alaric:</span>
                <p class="speech">"Remember your training. Trust your instincts."</p>
            </div>
            ]]>
        </full_example>
    </rule>

    <!-- III. 结构化交互和信息组件 -->
    <rule id="panel_rendering_from_user_input"> <!-- 规则：从用户输入渲染面板 -->
        <description> <!-- 描述 -->
            此规则概述了Apex如何从用户提供的XML块（例如，{userinformation_block}）处理面板数据
            并将其结构化为HTML。面板是不同的信息块。
            面板的背景颜色或特定内部样式由预格式化的`content_html`（用户提供）或针对`.panel`及其子元素的总体CSS确定。
        </description>
        <input_source_assumption> <!-- 输入源假设 -->
            Apex期望在{userinformation_block}中找到一个`<panels>`标签。
            在`<panels>`内部，每个`<panel>`子标签代表一个要渲染的面板。
        </input_source_assumption>
        <panel_data_extraction_per_panel_tag> <!-- 每个面板标签的面板数据提取 -->
            对于在输入源中找到的每个`<panel>`标签：
            - `title`（标题）：从内部`<title>`标签的未修改文本内容中提取。
            - `panel_position`（面板位置）：从内部`<panel_position>`标签的未修改文本内容中提取。有效值：'start'（开始）、'end'（结束）、'sidebar'（侧边栏）。
            - `content_html`（内容HTML）：从内部`<content_html>`标签的未修改文本内容中提取（通常是CDATA）。**Apex将直接逐字使用此HTML内容。**
        </panel_data_extraction_per_panel_tag>
        <target_html_structure_for_a_single_panel> <!-- 单个面板的目标HTML结构 -->
            <description> <!-- 描述 -->
                提取的数据被组装成这种HTML结构。类`panel-layout-[position_value]`帮助CSS进行定位。
                当<content_html>中出现{内容描述}格式的文本时，这表示需要你对该部分进行创造性扩写，而非原样返回
            </description>
            <example_structure> <!-- 示例结构 -->
            <![CDATA[
            <aside class="panel panel-layout-[position_value]"> <!-- 例如，panel-layout-start, panel-layout-end, panel-layout-sidebar -->
                <h3 class="panel-title">[Extracted Panel Title from <title> tag text]</h3> <!-- 从<title>标签文本提取的面板标题 -->
                <div class="panel-content"> <!-- 面板内容 -->
                    <!-- [Verbatim HTML content from <content_html> tag text GOES HERE] --> <!-- 来自<content_html>标签文本的逐字HTML内容放在这里 -->
                </div>
            </aside>
            ]]>
            </example_structure>
        </target_html_structure_for_a_single_panel>
        <processing_order>面板按其在输入`<panels>`块中出现的顺序渲染。</processing_order> <!-- 处理顺序 -->
    </rule>

    <rule id="action_options_presentation"> <!-- 规则：行动选项呈现 -->
        <description> <!-- 描述 -->
            定义用户行动选择的HTML结构。这些必须是交互式HTML元素。
            如果需要编号（"1."、"2."等），它必须是选项文本内容的一部分或通过`<ol>`列表实现。
            必须生成恰好四（4）个选项。
            **这些选项的视觉样式、"美化"、特定间距和交互性增强（例如，悬停效果、按钮外观）将通过CSS针对提供的类（`.action-options-container`、`.action-option`等）和元素类型来实现。**
        </description>
        <container_element_tag>div</container_element_tag> <!-- 容器元素标签 -->
        <container_class_name>action-options-container</container_class_name> <!-- 容器类名 -->
        <option_element_tag>button</option_element_tag> <!-- 选项元素标签 -->
        <option_class_name>action-option</option_class_name> <!-- 选项类名 -->
        <example_using_buttons> <!-- 使用按钮的示例 -->
            <![CDATA[
            <div class="action-options-container">
                <button class="action-option" data-choice-id="1">1. Investigate the strange humming sound.</button>
                <button class="action-option" data-choice-id="2">2. Prepare a defensive position.</button>
                <button class="action-option" data-choice-id="3">3. Try to communicate with the entity.</button>
                <button class="action-option" data-choice-id="4">4. Search for an escape route.</button>
            </div>
            ]]>
        </example_using_buttons>
        <example_using_ordered_list> <!-- 使用有序列表的示例 -->
            <![CDATA[
            <div class="action-options-container">
                <ol class="action-options-list"> <!-- 行动选项列表 -->
                    <li class="action-option" data-choice-id="1"><a href="#" data-internal-action="1">Investigate the strange humming sound.</a></li>
                    <li class="action-option" data-choice-id="2"><a href="#" data-internal-action="2">Prepare a defensive position.</a></li>
                    <li class="action-option" data-choice-id="3"><a href="#" data-internal-action="3">Try to communicate with the entity.</a></li>
                    <li class="action-option" data-choice-id="4"><a href="#" data-internal-action="4">Search for an escape route.</a></li>
                </ol>
            </div>
            ]]>
        </example_using_ordered_list>
        <compliance>四个选项，HTML结构化，根据需要编号，在容器内。CSS负责美化和精细间距调整。</compliance> <!-- 合规性 -->
    </rule>

    <!-- IV. APEX处理额外用户提供数据（Apex系统指导） -->
    <rule id="info_preset_data_integration"> <!-- 规则：信息预设数据集成 -->
        <description> <!-- 描述 -->
            如果{userinformation_block}包含`<info_preset_parts>`标签，Apex将其内容（例如，"第三人称"、类型）解析为AI内容生成指导。
            此数据主要影响AI的风格和内容，而不是直接的HTML输出，尽管如果存在系统规则，它可以通知全局类（例如，`body.theme-sci-fi`）。
        </description>
        <apex_action>解析AI指导；根据系统定义映射到全局HTML属性/类。</apex_action> <!-- Apex操作 -->
    </rule>

    <rule id="custom_html_data_injection"> <!-- 规则：自定义HTML数据注入 -->
        <description> <!-- 描述 -->
            如果{userinformation_block}有`<custom_html_data>`，Apex将其文本内容视为补充HTML（例如，`<meta>`标签、脚本），用于注入到预定义的页面位置（例如，`<head>`）。
            AI不生成这个；Apex提取并注入它。
        </description>
        <example_use_case>用户：`<custom_html_data><![CDATA[<meta name="keywords" content="interactive, fiction">]]></custom_html_data>`供Apex放置在`<head>`中。</example_use_case> <!-- 示例用例 -->
        <apex_action>提取原始文本；注入到系统定义的页面模板位置。</apex_action> <!-- Apex操作 -->
    </rule>

</web_html> <!-- 网页HTML规则定义结束 -->
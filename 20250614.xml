<web_html>
    <description>
        This block defines rules for how Apex and the AI should generate and structure the final HTML output for each turn.
        It includes HTML structural definitions, class naming conventions, and styling guidelines.
        The AI's generated content for a turn should form a cohesive block of HTML (potentially a single outer div containing all elements for the turn) that can be integrated into a webpage.
        Apex processes user-provided data (like Panels) according to these rules and combines it with AI-generated narrative content.
        Adherence to these rules ensures consistent and styleable output.

        Color definitions provided are primarily for guiding external CSS stylesheets. Generally, background colors should not be applied directly by the AI to elements via inline styles; CSS should manage this, with the possible exception of pre-formatted `content_html` within Panels.

        **Important Note on Visual Presentation:** Control over visual spacing (margins, padding between elements) and "beautification" (aesthetic styling of elements like buttons, text, etc.) is primarily managed by external CSS targeting the classes and HTML elements defined herein. This `web_html` focuses on correct structure and semantic meaning, which enables effective CSS application.
    </description>

    <!-- I. OUTPUT FORMAT AND GLOBAL CONSTRAINTS -->
    <rule id="output_format_html_only">
        <description>The AI must generate content as well-formed HTML. Markdown or other formats are not to be used for the final output stage described by these rules.</description>
        <compliance>Mandatory: HTML only.</compliance>
    </rule>

    <rule id="global_styling_restrictions">
        <description>
            Do not apply inline styles or define global block-level styles directly to the &lt;html&gt; or &lt;body&gt; tags within the AI's output.
            Rely on CSS classes applied to semantic HTML elements for all styling. This maintains HTML purity and CSS manageability.
        </description>
        <guideline>Use classes for styling; avoid inline styles on html/body tags or global undefined styles.</guideline>
    </rule>

    <rule id="image_resource_policy">
        <description>
            External image links (http/https URLs to images hosted on other domains or external paths) are strictly prohibited.
            If images are necessary and used, they must be embedded as Base64 data URIs within &lt;img&gt; tags or, if the system architecture supports it, referenced via internal project paths known to the rendering environment.
        </description>
        <example_base64_image>
            <![CDATA[<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==" alt="Small embedded red dot">]]>
        </example_base64_image>
        <compliance>No external image URLs. Use Base64 or verified internal paths only.</compliance>
    </rule>

    <!-- II. CORE NARRATIVE ELEMENTS: STRUCTURE AND STYLING HINTS (Colors for CSS Guidance) -->
    <rule id="narrative_and_action_text">
        <description>Defines HTML structure and class names for narrative and action descriptions. Color hints are primarily for external CSS guidance; the AI should not embed these as inline styles. Spacing around these paragraphs will be determined by browser defaults and then refined by CSS.</description>
        <element_type class_name="narrative">
            <purpose>General narration, scene descriptions, environmental details, character thoughts (if not otherwise specified).</purpose>
            <html_tag>p</html_tag>
            <styling_hint_for_css color="#555D65" comment="深灰色 (Deep Gray / Slate Gray)"/>
            <example><![CDATA[<p class="narrative">The ancient library was silent, save for the rustling of turning pages in the distance.</p>]]></example>
        </element_type>
        <element_type class_name="action">
            <purpose>Descriptions of character physical actions, significant events unfolding initiated by characters or the environment.</purpose>
            <html_tag>p</html_tag>
            <styling_hint_for_css color="#7f8c8d" comment="灰色 (Asbestos / Wet Asphalt like gray)"/>
            <example><![CDATA[<p class="action">He quickly drew his sword, the metal singing in the dim light.</p>]]></example>
        </element_type>
    </rule>

    <rule id="dialogue_presentation">
        <description>
            Defines HTML structure for character dialogue.
            - The `.speaker` element (character name): Text color MUST be `#FFFFFF` (white). For readability, its container or itself MUST have a sufficiently dark background (e.g., an orange hue or the theme's primary color), which should be applied via CSS by targeting `.speaker` or `.dialogue-entry`.
            - The `.speech` element (dialogue content): Text color MUST be `#FF7F50` (Coral Orange).
            The AI should ensure correct classes are applied; colors are primarily for CSS. Spacing within and around dialogue entries is controlled by CSS.
        </description>
        <container_tag>div</container_tag>
        <container_class>dialogue-entry</container_class>
        <speaker_element>
            <html_tag>span</html_tag>
            <class_name>speaker</class_name>
            <styling_hint_for_css color="#FFFFFF" comment="白色 (White). CSS MUST provide a dark background for this element or its container."/>
            <example><![CDATA[<span class="speaker">Elara Vancroft:</span>]]></example>
        </speaker_element>
        <speech_element>
            <html_tag>p</html_tag> <!-- Can also be span if inline with speaker, but p is often better for multi-line speech -->
            <class_name>speech</class_name>
            <styling_hint_for_css color="#FF7F50" comment="珊瑚橙 (Coral Orange)"/>
            <example><![CDATA[<p class="speech">"The artifact we seek lies beyond the Whispering Pass."</p>]]></example>
        </speech_element>
        <full_example>
            <![CDATA[
            <div class="dialogue-entry">
                <span class="speaker">Master Alaric:</span>
                <p class="speech">"Remember your training. Trust your instincts."</p>
            </div>
            ]]>
        </full_example>
    </rule>

    <!-- III. STRUCTURED INTERACTIVE AND INFORMATIONAL COMPONENTS -->
    <rule id="panel_rendering_from_user_input">
        <description>
            This rule outlines how Apex processes panel data from a user-provided XML block (e.g., {userinformation_block})
            and structures this into HTML. Panels are distinct informational blocks.
            The background color or specific internal styling of panels is determined by the pre-formatted `content_html` (provided by the user) or by overarching CSS targeting `.panel` and its children.
        </description>
        <input_source_assumption>
            Apex expects to find a `<panels>` tag in the {userinformation_block}.
            Inside `<panels>`, each `<panel>` child tag represents one panel to be rendered.
        </input_source_assumption>
        <panel_data_extraction_per_panel_tag>
            For each `<panel>` tag found in the input source:
            - `title`: Extracted from the UNMODIFIED text content of an inner `<title>` tag.
            - `panel_position`: Extracted from the UNMODIFIED text content of an inner `<panel_position>` tag. Valid values: 'start', 'end', 'sidebar'.
            - `content_html`: Extracted from the UNMODIFIED text content of an inner `<content_html>` tag (typically CDATA). **Apex will use this HTML content DIRECTLY and VERBATIM.**
        </panel_data_extraction_per_panel_tag>
        <target_html_structure_for_a_single_panel>
            <description>
                The extracted data is assembled into this HTML structure. The class `panel-layout-[position_value]` assists CSS in positioning.
                当<content_html>中出现{内容描述}格式的文本时，这表示需要你对该部分进行创造性扩写，而非原样返回
            </description>
            <example_structure>
            <![CDATA[
            <aside class="panel panel-layout-[position_value]"> <!-- e.g., panel-layout-start, panel-layout-end, panel-layout-sidebar -->
                <h3 class="panel-title">[Extracted Panel Title from <title> tag text]</h3>
                <div class="panel-content">
                    <!-- [Verbatim HTML content from <content_html> tag text GOES HERE] -->
                </div>
            </aside>
            ]]>
            </example_structure>
        </target_html_structure_for_a_single_panel>
        <processing_order>Panels are rendered in their order of appearance in the input `<panels>` block.</processing_order>
    </rule>

    <rule id="action_options_presentation">
        <description>
            Defines the HTML structure for user action choices. These MUST be interactive HTML elements.
            If numbering ("1.", "2.", etc.) is required, it MUST be part of the option's text content or achieved via an `<ol>` list.
            Exactly four (4) options must be generated.
            **Visual styling, 'beautification,' specific spacing, and interactivity enhancements (e.g., hover effects, button appearance) for these options are to be implemented via CSS targeting the provided classes (`.action-options-container`, `.action-option`, etc.) and element types.**
        </description>
        <container_element_tag>div</container_element_tag>
        <container_class_name>action-options-container</container_class_name>
        <option_element_tag>button</option_element_tag>
        <option_class_name>action-option</option_class_name>
        <example_using_buttons>
            <![CDATA[
            <div class="action-options-container">
                <button class="action-option" data-choice-id="1">1. Investigate the strange humming sound.</button>
                <button class="action-option" data-choice-id="2">2. Prepare a defensive position.</button>
                <button class="action-option" data-choice-id="3">3. Try to communicate with the entity.</button>
                <button class="action-option" data-choice-id="4">4. Search for an escape route.</button>
            </div>
            ]]>
        </example_using_buttons>
        <example_using_ordered_list>
            <![CDATA[
            <div class="action-options-container">
                <ol class="action-options-list">
                    <li class="action-option" data-choice-id="1"><a href="#" data-internal-action="1">Investigate the strange humming sound.</a></li>
                    <li class="action-option" data-choice-id="2"><a href="#" data-internal-action="2">Prepare a defensive position.</a></li>
                    <li class="action-option" data-choice-id="3"><a href="#" data-internal-action="3">Try to communicate with the entity.</a></li>
                    <li class="action-option" data-choice-id="4"><a href="#" data-internal-action="4">Search for an escape route.</a></li>
                </ol>
            </div>
            ]]>
        </example_using_ordered_list>
        <compliance>Four options, HTML structured, numbered as required, within a container. CSS is responsible for beautification and fine-tuned spacing.</compliance>
    </rule>

    <!-- IV. APEX PROCESSING OF ADDITIONAL USER-PROVIDED DATA (Guidance for Apex System) -->
    <rule id="info_preset_data_integration">
        <description>
            If the {userinformation_block} contains an `<info_preset_parts>` tag, Apex parses its content (e.g., "第三人称", genre) as AI content generation guidance.
            This data primarily influences AI style and substance, not direct HTML output, though it could inform global classes (e.g., `body.theme-sci-fi`) if system rules exist.
        </description>
        <apex_action>Parse for AI guidance; map to global HTML attributes/classes per system definition.</apex_action>
    </rule>

    <rule id="custom_html_data_injection">
        <description>
            If {userinformation_block} has `<custom_html_data>`, Apex treats its TEXT content as supplementary HTML (e.g., `<meta>` tags, scripts) for injection into predefined page locations (e.g., `<head>`).
            The AI doesn't generate this; Apex extracts and injects it.
        </description>
        <example_use_case>User: `<custom_html_data><![CDATA[<meta name="keywords" content="interactive, fiction">]]></custom_html_data>` for Apex to place in `<head>`.</example_use_case>
        <apex_action>Extract raw text; inject into system-defined page template locations.</apex_action>
    </rule>

</web_html>
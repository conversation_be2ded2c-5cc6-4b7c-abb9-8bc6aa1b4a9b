<web_html>
    <description>
        此区块定义了 Apex 和 AI 应如何生成和结构化最终 HTML 输出的规则。
        它包括 HTML 结构定义和类命名约定。
        AI 生成的内容应形成一个可以集成到网页中的 HTML 区块。
        Apex 根据这些规则处理来自系统提供源的数据，并将其与 AI 生成的内容相结合。
        遵守这些规则可确保外部样式表具有一致的结构。

        **关于视觉呈现的重要说明：** 对视觉呈现的控制主要通过针对此处定义的类和 HTML 元素的外部 CSS 进行管理。此 `web_html` 专注于正确的结构和语义含义。
    </description>

    <!-- I. 输出格式和全局约束 -->
    <rule id="output_format_html_only">
        <description>AI 必须生成格式良好的 HTML 内容。不得使用 Markdown 或其他格式。</description>
        <compliance>强制：仅 HTML。</compliance>
    </rule>

    <rule id="global_styling_restrictions">
        <description>
            不要在 AI 的输出中直接对 &lt;html&gt; 或 &lt;body&gt; 标签应用内联样式或定义全局块级样式。
            应依赖应用于语义 HTML 元素的 CSS 类进行所有样式化。
        </description>
        <guideline>使用类进行样式化；避免在 html/body 标签上使用内联样式。</guideline>
    </rule>

    <rule id="image_resource_policy">
        <description>
            严禁使用外部图像链接。
            如果需要使用图像，则必须将其作为 Base64 数据 URI 嵌入到 &lt;img&gt; 标签内，或通过内部项目路径引用。
        </description>
        <example_base64_image>
            <![CDATA[<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==" alt="示例图片">]]>
        </example_base64_image>
        <compliance>禁止外部图像 URL。仅使用 Base64 或内部路径。</compliance>
    </rule>

    <!-- II. HTML元素结构定义 -->
    <rule id="text_elements">
        <description>定义文本元素的 HTML 结构和类名。所有视觉样式均由外部 CSS 决定。</description>
        <element_type class_name="narrative">
            <html_tag>p</html_tag>
            <example><![CDATA[<p class="narrative">文本内容</p>]]></example>
        </element_type>
        <element_type class_name="action">
            <html_tag>p</html_tag>
            <example><![CDATA[<p class="action">文本内容</p>]]></example>
        </element_type>
    </rule>

    <rule id="dialogue_structure">
        <description>
            定义对话的 HTML 结构。
            - `.speaker` 元素（角色名称）
            - `.speech` 元素（对话内容）
            AI 使用正确的类。所有视觉样式均由外部 CSS 控制。
        </description>
        <container_tag>div</container_tag>
        <container_class>dialogue-entry</container_class>
        <speaker_element>
            <html_tag>span</html_tag>
            <class_name>speaker</class_name>
            <example><![CDATA[<span class="speaker">角色名：</span>]]></example>
        </speaker_element>
        <speech_element>
            <html_tag>p</html_tag>
            <class_name>speech</class_name>
            <example><![CDATA[<p class="speech">"对话内容"</p>]]></example>
        </speech_element>
        <full_example>
            <![CDATA[
            <div class="dialogue-entry">
                <span class="speaker">角色名：</span>
                <p class="speech">"对话内容"</p>
            </div>
            ]]>
        </full_example>
    </rule>
    
    <!-- III. 交互式选项结构 -->
    <rule id="action_options_structure">
        <description>
            定义用户操作选项的 HTML 结构。这些是可交互的 HTML 元素。
            如果需要编号，则它必须是选项文本内容的一部分或通过 `<ol>` 列表实现。
            AI 必须生成恰好四个选项。
            所有视觉样式将通过 CSS 来实现。
        </description>
        <container_element_tag>div</container_element_tag>
        <container_class_name>action-options-container</container_class_name>
        <option_element_tag>button</option_element_tag>
        <option_class_name>action-option</option_class_name>
        <example_using_buttons>
            <![CDATA[
            <div class="action-options-container">
                <button class="action-option" data-choice-id="1">1. 选项一</button>
                <button class="action-option" data-choice-id="2">2. 选项二</button>
                <button class="action-option" data-choice-id="3">3. 选项三</button>
                <button class="action-option" data-choice-id="4">4. 选项四</button>
            </div>
            ]]>
        </example_using_buttons>
        <compliance>四个选项，HTML 结构化，按需编号，包含在一个容器内。CSS 负责样式。</compliance>
    </rule>

    <!-- IV. 面板处理规则 -->
    <rule id="panel_processing">
        <description>
            此规则概述了 Apex 如何处理来自 `{userinformation_block}` 中的面板数据，并将其结构化为 HTML。
            面板的视觉样式由预格式化的 `content_html` 或针对 `.panel` 的 CSS 决定。
        </description>
        <input_source_assumption>
            Apex 期望在 `{userinformation_block}` 中找到一个 `<panels>` 标签。
            在 `<panels>` 内部，每个 `<panel>` 子标签代表一个要渲染的面板。
        </input_source_assumption>
        <panel_data_extraction_per_panel_tag>
            对于在 `{userinformation_block}` 中找到的每个 `<panel>` 标签：
            - `title`：从内部 `<title>` 标签的文本内容中提取
            - `panel_position`：从内部 `<panel_position>` 标签的文本内容中提取。有效值：'start'、'end'、'sidebar'
            - `content_html`：从内部 `<content_html>` 标签的文本内容中提取。Apex 将直接使用此 HTML 内容
        </panel_data_extraction_per_panel_tag>
        <target_html_structure_for_a_single_panel>
            <description>
                提取的数据被组装到此 HTML 结构中。类 `panel-layout-[position_value]` 辅助 CSS 进行定位。
            </description>
            <example_structure>
            <![CDATA[
            <aside class="panel panel-layout-[position_value]">
                <h3 class="panel-title">[面板标题]</h3>
                <div class="panel-content">
                    <!-- [HTML 内容直接放置于此] -->
                </div>
            </aside>
            ]]>
            </example_structure>
        </target_html_structure_for_a_single_panel>
        <processing_order>面板按照它们在 `<panels>` 区块中出现的顺序进行渲染。</processing_order>
    </rule>

    <!-- V. 系统数据处理 -->
    <rule id="info_preset_processing">
        <description>
            如果 `{userinformation_block}` 包含 `<info_preset_parts>` 标签，Apex 会解析其内容作为 AI 处理指导。
            此数据主要影响 AI 的输出，而非直接的 HTML 输出。
        </description>
        <apex_action>从 `{userinformation_block}` 中解析指导信息；根据系统定义映射到全局 HTML 属性/类。</apex_action>
    </rule>

    <rule id="custom_html_injection">
        <description>
            如果 `{userinformation_block}` 包含 `<custom_html_data>`，Apex 会将其文本内容视为补充性 HTML，用于注入到预定义的页面位置。
            AI 不生成此内容；Apex 从 `{userinformation_block}` 中提取并注入它。
        </description>
        <example_use_case>系统提供：`<custom_html_data><![CDATA[<meta name="keywords" content="interactive">]]></custom_html_data>`，供 Apex 放置在 `<head>` 中。</example_use_case>
        <apex_action>从 `{userinformation_block}` 中提取原始文本；注入到系统定义的页面模板位置。</apex_action>
    </rule>

</web_html>
